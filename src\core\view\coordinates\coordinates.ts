import { Annotation, AnnotationsPlugin, PickResult } from "@xeokit/xeokit-sdk";
import GlobalStore from "../../../core/globalStore/globalStore";
import mathTs from "../../../libs/mathTs";
import { OverlappingPicker } from "../../../libs/overlappingPick";
import { createUUID } from "../../../utils/utils";

export class Coordinates {
    private _store = GlobalStore.getInstance().getAll();
    private picker = new OverlappingPicker();
    private _mouseDownPos: { x: number; y: number } | null = null;
    private _isDragging = false;
    private _canvas: HTMLCanvasElement | null = null;
    private _listAnnotations: Map<string, Annotation> = new Map<string, Annotation>();
    private _isRemovingAnnotation = false;
    private _hightlighedMarketSelected: Annotation | undefined;
    private _annotations = new AnnotationsPlugin(this._store.viewer!, {
        container: this._store.containerCanvas as HTMLElement,
        labelHTML: "<div class='dpu-annotation-label' style='background-color: {{labelBGColor}};'><div class='annotation-title'>{{title}}</div><div class='annotation-desc'>{{description}}</div></div>",

        values: {
            markerBGColor: "red",
            description: "No description"
        },
        surfaceOffset: 0.01
    });

    constructor() {
        this._canvas = this._store.canvas ?? null;
        if (!this._canvas) {
            console.error("Canvas not found in store.");
            return;
        }
        this.initialize();
        GlobalStore.getInstance().set("coordinates", this);



    }

    initialize = () => {
        this._annotations.on("markerMouseEnter", (anotation: Annotation) => {
            //handle marker mouse enter
            if (this._isRemovingAnnotation) {
                console.log("Marker mouse enter", anotation);
                this._hightlighedMarketSelected = anotation;
                const marker = (anotation as any)._label as HTMLElement;
                const label = (anotation as any)._marker as HTMLElement;
                marker.classList.add("active");
                label.classList.add("active");
            }
        })

        this._annotations.on("markerMouseLeave", (anotation: Annotation) => {
            //handle marker mouse leave
            if (this._isRemovingAnnotation) {
                const marker = (anotation as any)._label as HTMLElement;
                const label = (anotation as any)._marker as HTMLElement;
                marker.classList.remove("active");
                label.classList.remove("active");
                this._hightlighedMarketSelected = undefined;
            }
        });


    }
    //#region actionCreateAnnotation
    actionCreateAnnotation = () => {

        this._canvas?.addEventListener("mousedown", this._handleMouseDown);
        this._canvas?.addEventListener("mousemove", this._handleMouseMove);
        this._canvas?.addEventListener("mouseup", this._handleMouseUp);
        //For trigger select again
        this._store.containerCanvas?.addEventListener("click", this._onclickRemove as EventListener);

    }

    //#region _handleMouseDown
    private _handleMouseDown = (event: MouseEvent) => {
        if (event.button !== 0) return;
        this._mouseDownPos = { x: event.offsetX, y: event.offsetY };
        this._isDragging = false;
    };
    //#region _handleMouseMove
    private _handleMouseMove = (event: MouseEvent) => {
        if (!this._mouseDownPos) return;
        const dx = Math.abs(event.offsetX - this._mouseDownPos.x);
        const dy = Math.abs(event.offsetY - this._mouseDownPos.y);
        const distance = Math.sqrt(dx * dx + dy * dy);
        if (distance > 5) this._isDragging = true;
    };
    //#region _handleMouseUp
    private _handleMouseUp = (event: MouseEvent) => {
        if (event.button !== 0 || !this._mouseDownPos) return;
        if (!this._isDragging) {
            this._createAnnotation(event)
        };
        this._mouseDownPos = null;
        this._isDragging = false;
    };
    //#region _createAnnotation
    private _createAnnotation = (event: MouseEvent) => {
        this._isRemovingAnnotation = false;
        const viewer = this._store.viewer!;
        const scene = viewer.scene;
        const camera = viewer.camera;
        const canvas = this._store.canvas!;

        const origin = mathTs.vec3();
        const direction = mathTs.vec3();

        // Reset overlappingPick trước mỗi click
        this.picker.reset();

        // Tính ray từ chuột
        mathTs.canvasPosToWorldRay(
            canvas,
            new Float64Array(camera.viewMatrix),
            new Float64Array(camera.projMatrix),
            camera.projection,
            [event.clientX, event.clientY],
            origin,
            direction
        );

        // Convert Float64Array to number[] for compatibility with PickRay interface
        const originArr = Array.from(origin);
        const directionArr = Array.from(direction);

        let hit: PickResult | null = null;

        while (true) {
            hit = this.picker.pickSurface(scene, { origin: originArr, direction: directionArr }, { wrapAround: false });
            if (!hit || !hit.entity) break;

            if (!hit.entity.xrayed) {
                break; // gặp thằng không xray đầu tiên thì break
            }
        }
        if (hit) {
            const idAnotation = createUUID(); // Generate a unique ID for the annotation
            const posX = hit.worldPos[0].toFixed(4);
            const posY = hit.worldPos[1].toFixed(4);
            const posZ = hit.worldPos[2].toFixed(4);
            const formatNumber = (num: string) => {
                return Number(num) >= 0 ? ` ${Number(num).toFixed(4)}` : Number(num).toFixed(4);
            };
            const annotation = this._annotations.createAnnotation({
                id: idAnotation, // Generate a unique ID for the annotation
                pickResult: hit, // <<------- initializes worldPos and entity from PickResult
                occludable: false,       // Optional, default is true
                markerShown: true,      // Optional, default is true
                labelShown: true,       // Optional, default is true
                values: {
                    markerBGColor: "red",
                    glyph: "",

                },
                markerHTML: `<div id='dpu-annotation-marker-${idAnotation}'  class='dpu-annotation-marker' style='background-color: {{markerBGColor}};'>{{glyph}}</div>`,
                labelHTML: `
        <div id="dpu-annotation-label-${idAnotation}" class="dpu-annotation-label" style="background-color: {{labelBGColor}}">
            <div class="dpu-annotation-content" >
                <span class='dpu-content-x'>X:</span>
                <span style="min-width: 60px; text-align: right;">${formatNumber(posX)}</span>
            </div>
            <div class="dpu-annotation-content" >
                <span class='dpu-content-y'>Y:</span>
                <span style="min-width: 60px; text-align: right;">${formatNumber(posY)}</span>
            </div>
            <div class="dpu-annotation-content" >
                <span class='dpu-content-z'>Z:</span>
                <span style="min-width: 60px; text-align: right;">${formatNumber(posZ)}</span>
            </div>
        </div>`

            });


            this._listAnnotations.set(annotation.id, annotation);

        }

    }
    //#region actionRemovebyClickAnnotation
    actionRemovebyClickAnnotation = () => {
        this._isRemovingAnnotation = true;
        this._store.containerCanvas?.addEventListener("click", this._onclickRemove as EventListener);

        this._canvas?.removeEventListener("mousedown", this._handleMouseDown);
        this._canvas?.removeEventListener("mousemove", this._handleMouseMove);
        this._canvas?.removeEventListener("mouseup", this._handleMouseUp);
    }

    //#region actionShowHideAllAnnotations
    actionShowHideAllAnnotations = (show: boolean) => {
        this._store.containerCanvas?.removeEventListener("click", this._onclickRemove as EventListener);

        this._listAnnotations.forEach((annotation) => {
            annotation.setLabelShown(show);
            annotation.setMarkerShown(show);
        });
    }

    //#region _onclickRemove
    private _onclickRemove = (e: MouseEvent) => {
        e.preventDefault();
        if (this._hightlighedMarketSelected) {
            this._removeAnnotation(this._hightlighedMarketSelected.id);
        }
    }

    //#region _removeAnnotation
    private _removeAnnotation = (id: string) => {
        const annotation = this._listAnnotations.get(id);
        if (annotation) {
            this._annotations.destroyAnnotation(id);
            this._listAnnotations.delete(id);
        }
    }
    //#region actionRemoveAllAnnotations
    actionRemoveAllAnnotations = () => {
        this._store.containerCanvas?.removeEventListener("click", this._onclickRemove as EventListener);

        this._listAnnotations.forEach((annotation, id) => {
            this._annotations.destroyAnnotation(id);
        });
        this._listAnnotations.clear();
    }

    //#region refreshActions
    refreshActions = () => {
        this._canvas?.removeEventListener("mousedown", this._handleMouseDown);
        this._canvas?.removeEventListener("mousemove", this._handleMouseMove);
        this._canvas?.removeEventListener("mouseup", this._handleMouseUp);
        this.actionShowHideAllAnnotations(false);

        this._isRemovingAnnotation = false;
        this._hightlighedMarketSelected = undefined;
        this._store.containerCanvas?.removeEventListener("click", this._onclickRemove as EventListener);
    }



}