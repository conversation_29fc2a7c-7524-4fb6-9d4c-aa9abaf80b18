import { getMessage } from "../../../language";
import GlobalStore from "../../../core/globalStore/globalStore";
import { IconEraser, IconEyeHide, IconEyeShow, IconMeasureMultiRuler, IconMeasureRuler, IconPointMarket, IconPointTool, IconRulerTool, IconTick, IconTrash } from "../../../utils/icon";
import { ToolbarOption } from "../../../types/toolbar/toolbarConfig";

export class Toolbar {
    private _store = GlobalStore.getInstance().getAll();
    private _finalOption: ToolbarOption | undefined = undefined

    private _buttonConfig = [
        //distance
        {
            id: `group-distance-${this._store.containerId}`,
            className: 'dpu-container-toolbar',
            show: true,
            buttons: [
                //Dim group
                {
                    id: `dim-group-${this._store.containerId}`,
                    className: 'dpu-container-btn-toolbar',
                    show: true,
                    tooltip: getMessage("tool-tip-measure"),
                    buttons: [
                        {
                            id: `ruler-tool-${this._store.containerId}`,
                            className: 'dpu-btn-toolbar',
                            tooltip: getMessage("tool-tip-measure"),
                            icon: IconRulerTool(),
                            show: true,
                            onClick: () => { this.ToolBarActionMeasure(`ruler-tool-${this._store.containerId}`, `${this._store.containerId}`) }
                        },
                        {
                            id: `measure-ruler-${this._store.containerId}`,
                            className: 'dpu-btn-toolbar',
                            tooltip: getMessage("tool-tip-measure-action"),
                            icon: IconMeasureRuler(),
                            show: false,
                            onClick: () => { this.ToolBarActionMeasure(`measure-ruler-${this._store.containerId}`, `${this._store.containerId}`) }
                        },
                        // {
                        //     id: `measure-multi-ruler-${this._store.containerId}`,
                        //     className: 'dpu-btn-toolbar',
                        //     tooltip: getMessage("tool-tip-measure-multi-action"),
                        //     icon: IconMeasureMultiRuler(),
                        //     show: false,
                        //     onClick: () => { this.ToolBarActionMeasure(`measure-multi-ruler-${this._store.containerId}`, `${this._store.containerId}`) }
                        // },
                        {
                            id: `eraser-measure-ruler-${this._store.containerId}`,
                            className: 'dpu-btn-toolbar',
                            tooltip: getMessage("tool-tip-measure-delete-pick"),
                            icon: IconEraser(),
                            show: false,
                            onClick: () => { this.ToolBarActionMeasure(`eraser-measure-ruler-${this._store.containerId}`, `${this._store.containerId}`) }
                        },
                        {
                            id: `eye-show-dim-${this._store.containerId}`,
                            className: 'dpu-btn-toolbar',
                            tooltip: getMessage("tool-tip-measure-visible"),
                            icon: IconEyeShow(),
                            show: false,
                            onClick: () => { this.ToolBarActionMeasure(`eye-show-dim-${this._store.containerId}`, `${this._store.containerId}`) }
                        },
                        {
                            id: `eye-hide-dim-${this._store.containerId}`,
                            className: 'dpu-btn-toolbar',
                            tooltip: getMessage("tool-tip-measure-hide"),
                            icon: IconEyeHide(),
                            show: false,
                            onClick: () => { this.ToolBarActionMeasure(`eye-hide-dim-${this._store.containerId}`, `${this._store.containerId}`) }
                        },
                        {
                            id: `remove-all-dim-${this._store.containerId}`,
                            className: 'dpu-btn-toolbar',
                            tooltip: getMessage("tool-tip-measure-delete-all"),
                            icon: IconTrash(),
                            show: false,
                            onClick: () => { this.ToolBarActionMeasure(`remove-all-dim-${this._store.containerId}`, `${this._store.containerId}`) }
                        },
                        {
                            id: `done-dim-${this._store.containerId}`,
                            className: 'dpu-btn-toolbar done',
                            tooltip: '',
                            icon: IconTick(),
                            show: false,
                            onClick: () => { this.ToolBarActionMeasure(`done-dim-${this._store.containerId}`, `${this._store.containerId}`) }
                        }
                    ]
                },
                //Point group
                {
                    id: `point-group-${this._store.containerId}`,
                    className: 'dpu-container-btn-toolbar',
                    show: true,
                    tooltip: 'Tọa độ điểm',
                    buttons: [
                        {
                            id: `point-tool-${this._store.containerId}`,
                            className: 'dpu-btn-toolbar',
                            tooltip: getMessage("tool-tip-coordinates"),
                            icon: IconPointTool(),
                            show: true,
                            onClick: () => { this.ToolBarActionPoint(`point-tool-${this._store.containerId}`, `${this._store.containerId}`) }
                        },
                        {
                            id: `point-pick-${this._store.containerId}`,
                            className: 'dpu-btn-toolbar',
                            tooltip: getMessage("tool-tip-coordinates-action"),
                            icon: IconPointMarket(),
                            show: false,
                            onClick: () => { this.ToolBarActionPoint(`point-pick-${this._store.containerId}`, `${this._store.containerId}`) }
                        },
                        {
                            id: `eraser-point-${this._store.containerId}`,
                            className: 'dpu-btn-toolbar',
                            tooltip: getMessage("tool-tip-coordinates-delete-pick"),
                            icon: IconEraser(),
                            show: false,
                            onClick: () => { this.ToolBarActionPoint(`eraser-point-${this._store.containerId}`, `${this._store.containerId}`) }
                        },
                        {
                            id: `eye-hide-point-${this._store.containerId}`,
                            className: 'dpu-btn-toolbar',
                            tooltip: getMessage("tool-tip-coordinates-hide"),
                            icon: IconEyeHide(),
                            show: false,
                            onClick: () => { this.ToolBarActionPoint(`eye-hide-point-${this._store.containerId}`, `${this._store.containerId}`) }
                        },
                        {
                            id: `eye-show-point-${this._store.containerId}`,
                            className: 'dpu-btn-toolbar',
                            tooltip: getMessage("tool-tip-coordinates-visible"),
                            icon: IconEyeShow(),
                            show: false,
                            onClick: () => { this.ToolBarActionPoint(`eye-show-point-${this._store.containerId}`, `${this._store.containerId}`) }
                        },
                        {
                            id: `remove-all-point-${this._store.containerId}`,
                            className: 'dpu-btn-toolbar',
                            tooltip: getMessage("tool-tip-coordinates-delete-all"),
                            icon: IconTrash(),
                            show: false,
                            onClick: () => { this.ToolBarActionPoint(`remove-all-point-${this._store.containerId}`, `${this._store.containerId}`) }
                        },
                        {
                            id: `done-point-${this._store.containerId}`,
                            className: 'dpu-btn-toolbar done',
                            tooltip: '',
                            icon: IconTick(),
                            show: false,
                            onClick: () => { this.ToolBarActionPoint(`done-point-${this._store.containerId}`, `${this._store.containerId}`) }
                        }
                    ]
                },

            ]
        },
    ]

    constructor(toolbarOption: ToolbarOption) {
        this._finalOption = toolbarOption; // Lưu tùy chọn toolbar vào biến
        this._store.toolbar = this; // Lưu instance vào store



        this.checkOptionToolbar(this._finalOption, this._buttonConfig)
        // Tạo toolbar từ buttonConfig

        const container = this._store.containerCanvas;
        if (!container) {
            console.error(`Container not found`);
            return;
        }

        // Tạo toolbar chính
        const toolbar = document.createElement('div');
        toolbar.className = 'dpu-custom-toolbar';

        // Đệ quy để render từng group
        this._buttonConfig.forEach(group => {
            if (group.show) {
                const groupElement = this.createButtonElement(group); // Sử dụng hàm đệ quy để tạo group
                if (groupElement) {
                    toolbar.appendChild(groupElement);
                }
            }
        })

        container.appendChild(toolbar); // Thêm toolbar vào container

        this._buttonConfig.forEach(group => {
            if (group.buttons && Array.isArray(group.buttons)) {
                // Kiểm tra nếu tất cả các button bên trong có show: false
                const allButtonsHidden = group.buttons.every(button => button.show === false);

                if (group.buttons.length == 0 || allButtonsHidden) {
                    // Query DOM theo id của nhóm và đặt display: none
                    // showHideElementByID(group.id, false);
                    // Query DOM theo id của nhóm
                    const element = document.getElementById(group.id);
                    if (element) {
                        element.remove(); // Xóa phần tử khỏi DOM
                    }
                }
            }
        });

    }

    //#region checkOptionToolbar
    checkOptionToolbar = (finalOptions: ToolbarOption, buttonConfig: any[]) => {
        Object.entries(finalOptions).forEach(([key, value]) => {
            if (key === 'measure' && !value) {
                this.removeButtonsConfigById(buttonConfig, [`dim-group-${this._store.containerId}`])
            }
        });
    }

    //#region ToolBarActionDim
    ToolBarActionMeasure = (idElement: string, containerId: string) => {
        //list để bật tắt các button
        const listBtnToggle = [
            `ruler-tool-${containerId}`,
            `measure-ruler-${containerId}`,
            // `measure-multi-ruler-${containerId}`,
            `eraser-measure-ruler-${containerId}`,
            `eye-show-dim-${containerId}`,
            `eye-hide-dim-${containerId}`,
            `remove-all-dim-${containerId}`,
            `done-dim-${containerId}`
        ]

        const listIdGroup = [
            `dim-group-${containerId}`,
            `point-group-${containerId}`,
            `label-properties-${containerId}`,
            `group-data-${containerId}`,
            `data-element-${containerId}`,
            `group-camera-${containerId}`,
            `fly-camera-${containerId}`
        ];
        const btnActiveGr = listBtnToggle.slice(1, listBtnToggle.length - 2);

        this._unActiveBtnInGroup(listBtnToggle)
        this._activeBtnInGroup(btnActiveGr, idElement)
        switch (idElement) {
            case `ruler-tool-${containerId}`:
                //Ẩn group k cần thiết
                this._showHideElementsByID(listIdGroup, false)
                this._showHideElementByID(`dim-group-${containerId}`, true)

                //Hiện ra các nút cần thiết
                this._showHideElementsByID(listBtnToggle, true)
                this._showHideElementByID(`ruler-tool-${containerId}`, false)

                this._showHideElementByID(`group-camera-${containerId}`, false);
                this._showHideElementByID(`group-data-${containerId}`, false);
                this._showHideElementByID(`group-note-${containerId}`, false);

                //Hủy select đối tượng
                this._store.eventHandler?.destroy();
                //Active nút dim trước
                this._activeBtn(`measure-ruler-${containerId}`, true)
                //Hiện hết DIM
                this._store.measure?.actionShowHideAllMeasurements(true)
                //Thực hiện Dim
                this._store.measure?.actionStartMeasure()

                //Hủy context Menu đi
                this._store.contextMenu?.removeEventListeners()


                // this.resetDialog(`data-element-${containerId}`)
                // this.resetDialog(`label-properties-${containerId}`)
                break;

            case `measure-ruler-${containerId}`:
                this._store.measure?.actionShowHideAllMeasurements(true)

                this._store.measure?.actionStartMeasure()
                // this._measure!.showHideAllDimensionCesium(true);
                // this._measure!.isMultiDim = false
                // this._measure!.dimensionCesium()
                break;
            case `measure-multi-ruler-${containerId}`:
                // this._measure!.isMultiDim = true

                // this._measure!.showHideAllDimensionCesium(true);
                // this._measure!.dimensionCesium()
                break;
            case `eraser-measure-ruler-${containerId}`:

                this._store.measure?.actionShowHideAllMeasurements(true)

                this._store.measure?.actionRemoveMeasurement()
                break;
            case `eye-show-dim-${containerId}`:
                this._store.measure?.actionShowHideAllMeasurements(true)

                break;
            case `eye-hide-dim-${containerId}`:
                this._store.measure?.actionShowHideAllMeasurements(false)


                break
            case `remove-all-dim-${containerId}`:
                this._store.measure?.actionRemoveAllMeasurements()

                break
            case `done-dim-${containerId}`:
                this._store.measure?.refreshAction()


                //Ẩn hết DIM
                this._store.measure?.actionShowHideAllMeasurements(false)

                //unactive hết tất ca
                this._unActiveBtnInGroup(listBtnToggle)
                //show lại group
                this._showHideElementsByID(listIdGroup, true)
                //Ẩn các nút 
                this._showHideElementsByID(listBtnToggle, false)
                //Show lại nút
                this._showHideElementByID(`ruler-tool-${containerId}`, true)
                this._showHideElementByID(`group-camera-${containerId}`, true);
                this._showHideElementByID(`group-data-${containerId}`, true);
                this._showHideElementByID(`group-note-${containerId}`, true);
                //tạo context Menu
                this._store.contextMenu?.initialize()

                //Bật lại sự kiện select đối tượng
                this._store.eventHandler?.initialize();
                break;
            default:
                break


        }

    }

    //#region Point
    ToolBarActionPoint = (idElement: string, containerId: string) => {
        const listBtnToggle = [
            `point-tool-${containerId}`,
            `point-pick-${containerId}`,
            `eraser-point-${containerId}`,
            `eye-show-point-${containerId}`,
            `eye-hide-point-${containerId}`,
            `remove-all-point-${containerId}`,
            `done-point-${containerId}`
        ];

        const listIdGroup = [
            `dim-group-${containerId}`,
            `point-group-${containerId}`,
            `label-properties-${containerId}`,
            `group-data-${containerId}`,
            `data-element-${containerId}`,
            `group-camera-${containerId}`,
            `fly-camera-${containerId}`
        ];

        const btnActiveGr = listBtnToggle.slice(1, listBtnToggle.length - 2);

        this._unActiveBtnInGroup(listBtnToggle);
        this._activeBtnInGroup(btnActiveGr, idElement);

        switch (idElement) {
            case `point-tool-${containerId}`:
                this._showHideElementsByID(listIdGroup, false);
                this._showHideElementByID(`point-group-${containerId}`, true);
                this._showHideElementsByID(listBtnToggle, true);
                this._showHideElementByID(`point-tool-${containerId}`, false);
                this._showHideElementByID(`group-camera-${containerId}`, false);
                this._showHideElementByID(`group-data-${containerId}`, false);
                this._showHideElementByID(`group-note-${containerId}`, false);
                this._activeBtn(`point-pick-${containerId}`, true);

                //Hủy context Menu
                this._store.contextMenu?.removeEventListeners()
                //Hủy select đối tượng
                this._store.eventHandler?.destroy();

                //Bật point
                this._store.coordinates?.actionShowHideAllAnnotations(true);
                this._store.coordinates?.actionCreateAnnotation()

                // this.resetDialog(`data-element-${containerId}`)
                // this.resetDialog(`label-properties-${containerId}`)

                break;

            case `point-pick-${containerId}`:
                this._store.coordinates?.actionShowHideAllAnnotations(true);
                this._store.coordinates?.actionCreateAnnotation()

                break;

            case `eraser-point-${containerId}`:
                this._store.coordinates?.actionShowHideAllAnnotations(true);
                this._store.coordinates?.actionRemovebyClickAnnotation()

                break;

            case `eye-show-point-${containerId}`:
                this._store.coordinates?.actionShowHideAllAnnotations(true);

                break;

            case `eye-hide-point-${containerId}`:
                this._store.coordinates?.actionShowHideAllAnnotations(false);

                break;
            case `remove-all-point-${containerId}`:
                this._store.coordinates?.actionRemoveAllAnnotations();

                break;


            case `done-point-${containerId}`:


                this._showHideElementsByID(listIdGroup, true);
                this._showHideElementsByID(listBtnToggle, false);
                this._showHideElementByID(`point-tool-${containerId}`, true);

                this._showHideElementByID(`group-camera-${containerId}`, true);
                this._showHideElementByID(`group-data-${containerId}`, true);
                this._showHideElementByID(`group-note-${containerId}`, true);

                //Toggle context Menu
                this._store.contextMenu?.initialize()
                //Toggle select element
                this._store.eventHandler?.initialize();
                //Turn off coordinates
                this._store.coordinates?.refreshActions();
                break;

            default:
                break;
        }
    };

    //#region removeButtonsConfigById
    removeButtonsConfigById = (buttonConfig: any[], idsToRemove: string[]): any[] => {
        return buttonConfig
            .map(group => {
                // Loại bỏ các buttons có id trong danh sách idsToRemove
                if (group.buttons && Array.isArray(group.buttons)) {
                    group.buttons = group.buttons.filter((button: any) => {
                        // Nếu button có id khớp, loại bỏ nó
                        if (idsToRemove.includes(button.id)) {
                            return false;
                        }
                        // Nếu button có nested buttons, gọi đệ quy để xử lý
                        if (button.buttons && Array.isArray(button.buttons)) {
                            button.buttons = button.buttons.filter((subButton: any) => !idsToRemove.includes(subButton.id));
                        }
                        return true;
                    });
                }

                // Kiểm tra nếu chính group.id nằm trong idsToRemove
                return !idsToRemove.includes(group.id);
            })
            .filter(group => group); // Loại bỏ các nhóm bị xóa hoàn toàn
    };


    //#region createButtonElement
    // Hàm tạo button với các thuộc tính đúng
    createButtonElement = (button: any) => {
        const buttonElement = document.createElement('div');
        buttonElement.id = button.id;
        buttonElement.className = button.className || '';

        // Nếu không hiển thị, ẩn thẻ button
        if (!button.show) {
            buttonElement.style.display = 'none';
        }

        // Thêm biểu tượng vào button (nếu có)
        if (button.icon) {
            buttonElement.innerHTML = button.icon;
        }

        // Thêm tooltip vào button (nếu có)
        if (button.tooltip) {
            buttonElement.setAttribute('data-tooltip', button.tooltip);
            // Gắn sự kiện để hiển thị và ẩn tooltip
            // buttonElement.addEventListener('mouseenter', (event) => this.handleMouseEnter(event, 1000));
            // buttonElement.addEventListener('mouseleave', this.handleMouseLeave);
        }

        // Đặt sự kiện click (nếu có)
        if (typeof button.onClick === 'function') {
            buttonElement.addEventListener('click', button.onClick);
        }

        // Nếu button có các button con
        if (button.buttons && Array.isArray(button.buttons) && button.buttons.length > 0) {
            // Duyệt qua các button con và đệ quy để tạo ra các button con
            button.buttons.forEach((subButton: any) => {
                const subButtonElement = this.createButtonElement(subButton);
                if (subButtonElement) {
                    buttonElement.appendChild(subButtonElement); // Gắn trực tiếp vào thẻ cha
                }
            });
        }



        return buttonElement;



    };

    //#region _unActiveBtnInGroup
    private _unActiveBtnInGroup = (listBtn: string[]) => {
        listBtn.forEach(id => {
            this._activeBtn(id, false);
        })
    }

    //#region _activeBtn
    private _activeBtn = (idTarget: string, active: boolean) => {
        const btnSelect = document.getElementById(idTarget)
        if (active) {
            btnSelect!.classList.add('active')
        } else {
            btnSelect!.classList.remove('active')
        }
    }

    //#region _activeBtnInGroup
    private _activeBtnInGroup = (listBtn: string[], idActive: string) => {
        listBtn.forEach(id => {
            this._activeBtn(id, false);
        })
        if (listBtn.includes(idActive)) {
            this._activeBtn(idActive, true)
        }
    }


    //#region _showHideElementsByID
    private _showHideElementsByID = (idElements: string[], showElements: boolean) => {
        idElements.forEach(id => {
            this._showHideElementByID(id, showElements);
        });
    }
    //#region _showHideElementByID
    private _showHideElementByID = (idElement: string, showElement: boolean) => {
        const element = document.getElementById(idElement);
        if (element) {
            if (showElement) {
                element!.style.removeProperty("display");
            } else {
                element!.style.display = "none";
            }
        }
    }

}