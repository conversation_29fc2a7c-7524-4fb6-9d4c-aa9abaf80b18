import { DistanceMeasurement, DistanceMeasurementsMouseControl, DistanceMeasurementsPlugin } from "@xeokit/xeokit-sdk";
import GlobalStore from "../../../core/globalStore/globalStore";

export class Measure {
    private _store = GlobalStore.getInstance().getAll();
    distanceMeasurementsPlugin = new DistanceMeasurementsPlugin(this._store.viewer!, {
        container: this._store.containerCanvas as HTMLElement,
        zIndex: 500

    });
    private distanceMeasurementsMouseControl: DistanceMeasurementsMouseControl | undefined; // Để sử dụng sau này



    constructor() {
        this.initialize()
        GlobalStore.getInstance().set("measure", this);
    }
    //#region initialize
    initialize = () => {
        if (this._store.viewer && this._store.viewer.cameraControl) {
            //Less snap to edge
            this._store.viewer.cameraControl.snapRadius = 5;
        }

        this.distanceMeasurementsPlugin.on("mouseOver", (e) => {
            if (this._isRemovingMeasure) {
                this._hightlighedMeasureSelected = e.measurement;

                e.measurement.setHighlighted(true);
            }
        });
        this.distanceMeasurementsPlugin.on("mouseLeave", (e) => {
            if (this._isRemovingMeasure) {
                e.measurement.setHighlighted(false);
                this._hightlighedMeasureSelected = undefined;
            }
        });



        if (!this.distanceMeasurementsMouseControl) {
            this.distanceMeasurementsMouseControl = new DistanceMeasurementsMouseControl(this.distanceMeasurementsPlugin, {
                snapping: true, // Default

            })

        }
        this.distanceMeasurementsMouseControl.snapping = true; // Bật snapping
        this.distanceMeasurementsMouseControl?.deactivate();

    }

    //#region _onClickRemove
    private _onClickRemove = (e: MouseEvent) => {
        e.preventDefault();
        if (this._hightlighedMeasureSelected) {
            this.distanceMeasurementsPlugin.destroyMeasurement(this._hightlighedMeasureSelected.id);
            this._hightlighedMeasureSelected = undefined;
        }
    };


    private _hightlighedMeasureSelected: DistanceMeasurement | undefined = undefined;
    private _isRemovingMeasure = false;
    //#region actionStartMeasure
    actionStartMeasure = () => {
        this.distanceMeasurementsMouseControl?.activate();
        this._isRemovingMeasure = false;

        this.destroyEventRemoveMeasurement();
        this._handleCancelMeasure();
    }
    //#region _onEscKeyDown
    private _onEscKeyDown = (e: KeyboardEvent) => {
        if (e.key === "Escape") {
            this.distanceMeasurementsMouseControl?.reset();
        }
    };

    //#region _onRightClick
    private _onRightClick = (e: Event) => {
        e.preventDefault();
        this.distanceMeasurementsMouseControl?.reset();
    };

    //#region _handleCancelMeasure
    private _handleCancelMeasure = () => {
        window.addEventListener("keydown", this._onEscKeyDown);
        this._store.containerCanvas?.addEventListener("contextmenu", this._onRightClick as EventListener);
    };
    //#region _removeCancelMeasureListeners
    private _removeCancelMeasureListeners = () => {
        window.removeEventListener("keydown", this._onEscKeyDown);
        this._store.containerCanvas?.removeEventListener("contextmenu", this._onRightClick);
    };

    //#region actionRemoveMeasurement
    actionRemoveMeasurement = () => {
        this.distanceMeasurementsMouseControl?.deactivate();
        this.distanceMeasurementsMouseControl?.reset();
        this._isRemovingMeasure = true;
        this._store.containerCanvas?.addEventListener("click", this._onClickRemove as EventListener);
        this._removeCancelMeasureListeners();
    }

    //#region actionShowHideAllMeasurements
    actionShowHideAllMeasurements = (show: boolean) => {
        const measurements = this.distanceMeasurementsPlugin.measurements;
        this.distanceMeasurementsMouseControl?.deactivate();

        this.distanceMeasurementsMouseControl?.reset();

        for (const measurementId in measurements) {
            const measurement = measurements[measurementId];
            if (measurement) {
                measurement.visible = show; //Ẩn/Hiển thị tất cả các đo lường
            }
        }

        this.destroyEventRemoveMeasurement();
        this._removeCancelMeasureListeners();

    }

    //#region actionRemoveAllMeasurements
    actionRemoveAllMeasurements = () => {
        const measurements = this.distanceMeasurementsPlugin.measurements;
        this.distanceMeasurementsMouseControl?.deactivate();

        this.distanceMeasurementsMouseControl?.reset();
        for (const measurementId in measurements) {
            const measurement = measurements[measurementId];
            if (measurement) {
                this.distanceMeasurementsPlugin.destroyMeasurement(measurement.id); // Xóa tất cả các đo lường
            }
        }
        this._removeCancelMeasureListeners();

    }

    //#region destroyEventRemoveMeasurement
    destroyEventRemoveMeasurement = () => {

        this._store.containerCanvas?.removeEventListener("click", this._onClickRemove! as EventListener);
    }

    //#region refreshAction
    refreshAction = () => {
        this._isRemovingMeasure = false;
        this._hightlighedMeasureSelected = undefined;
        this.distanceMeasurementsMouseControl?.deactivate();
        this.destroyEventRemoveMeasurement()
        this._removeCancelMeasureListeners();

    }
}