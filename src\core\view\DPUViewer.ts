import { GLTFLoaderPlugin, Viewer } from "@xeokit/xeokit-sdk";
import { createUUID } from "../../utils/utils";
import GlobalStore from "../globalStore/globalStore";
import { EventHandler } from "./interaction/eventHandler";
import { ContextMenu } from "./interaction/contextMenu";
import { SceneManager } from "./sceneManager/sceneManager";
import { Toolbar } from "./toolbar/toolbar";
import { ToolbarOption } from "../../types/toolbar/toolbarConfig";
import { Measure } from "./measure/measure";
import { Coordinates } from "./coordinates/coordinates";
import { SectionBox } from "./sections/sectionBox";

export class DPUViewer {
    private _idGeneral = createUUID()
    // Lấy instance
    private _store = GlobalStore.getInstance();
    private _viewer: Viewer | undefined;
    private _gltfLoader: GLTFLoaderPlugin | undefined;
    private _eventHandler: EventHandler | undefined;
    private _contextMenu: ContextMenu | undefined;
    private _sceneManager: SceneManager | undefined; // <PERSON><PERSON><PERSON> sử dụng, có thể xóa sau này
    private _toolbar: Toolbar | undefined; // Chưa sử dụng, có thể xóa sau này
    private _measure: Measure | undefined; // Chưa sử dụng, có thể xóa sau này
    private _coordinates: Coordinates | undefined; // Chưa sử dụng, có thể xóa sau này

    private _optionToolbar: ToolbarOption = {
        measure: true, // Mặc định bật Measure
    }
    constructor(element: Element, token?: string) {
        //Set token in global store
        this._store.set("token", token)

        this._initializeView(element);
    }

    private _initializeView(element: Element) {


        const containerCanvas = document.createElement("div");
        containerCanvas.id = `dpu-container-viewer-${this._idGeneral}`;
        containerCanvas.className = "dpu-container-view";
        element.appendChild(containerCanvas);

        const createCanvas = document.createElement("canvas");
        createCanvas.id = `dpu-viewer-${this._idGeneral}`;
        createCanvas.className = "dpu-viewer";
        containerCanvas.appendChild(createCanvas);

        this._viewer = new Viewer({
            canvasId: `dpu-viewer-${this._idGeneral}`,
        });
        // Set far plane default camera distance
        this._viewer.scene.camera.perspective.far = 1000000;
        const cameraControl = this._viewer.cameraControl;
        cameraControl.panRightClick = false; // Prevents right-click-drag panning interfering with ContextMenus
        // const input = this._viewer.scene.input;
        // const keyMap: any = {};
        // keyMap[cameraControl.MOUSE_PAN] = [input.MOUSE_MIDDLE_BUTTON];
        // //Change mouse pan to middle mouse button
        // cameraControl.keyMap = { ...cameraControl.keyMap, ...keyMap };



        // Set camera control mouse buttons
        // Lưu vào store
        this._store.set("viewer", this._viewer);
        this._store.set("idGeneral", this._idGeneral);
        this._store.set("containerViewer", element);
        this._store.set("containerCanvas", containerCanvas);
        this._store.set("canvas", createCanvas);
        this._store.set("canvasId", `dpu-viewer-${this._idGeneral}`);
        this._store.set("containerId", `dpu-container-viewer-${this._idGeneral}`);


        this._eventHandler = new EventHandler(); // Chưa sử dụng, có thể xóa sau này
        this._contextMenu = new ContextMenu(); // Chưa sử dụng, có thể xóa sau này
        this._sceneManager = new SceneManager(); // Chưa sử dụng, có thể xóa sau này
        this._toolbar = new Toolbar(this._optionToolbar); // Chưa sử dụng, có thể xóa sau này
        this._measure = new Measure(); // Chưa sử dụng, có thể xóa sau này 
        this._coordinates = new Coordinates(); // Chưa sử dụng, có thể xóa sau này
       
        console.log("store", this._store.getAll());
    }

    private _queue: Promise<any> = Promise.resolve();  // Thay vì Promise<void>, sử dụng Promise<any> để linh hoạt
    // Hàm thêm tác vụ vào hàng đợi
    private enqueue<T>(task: () => Promise<T>): Promise<T> {
        this._queue = this._queue
            .then(() => task()) // Đảm bảo rằng các tác vụ được xử lý tuần tự
            .catch(err => {
                console.log(err);
                // throw err; // Ném lỗi để các tác vụ sau đó có thể xử lý lỗi
            });
        return this._queue as Promise<T>;  // Ép kiểu trả về Promise<T>
    }

    public gltfLoad = ({ name, modelSrc }: { name: string, modelSrc: string }) => {

        this._sceneManager?.gtlfLoad(name, modelSrc);

    }
    public cityJsonLoad = async ({ name, modelSrc }: { name: string, modelSrc: string }) => {
        const respone = await fetch(modelSrc);
        const data = await respone.json();
        console.log(data);
        this._sceneManager?.cityJsonLoad(name, data);
    }
}