.dpu-annotation-marker {
  position: absolute;
  color: #ffffff;
  width: 15px;
  height: 15px;
  transform: translate(5px, 5px);
  border-radius: 50%;
  border: 2px solid #ffffff;
  background: black;
  visibility: hidden;
  box-shadow: 5px 5px 15px 1px rgba(0, 0, 0, 0.5);
  z-index: 0;
  font-weight: bold;
  transition: 0.1s all;
}

.dpu-annotation-marker.active {
  width: 18px;
  height: 18px;
  box-shadow: -1px 3px 11px 1px rgb(255 255 255 / 85%);
}

.dpu-annotation-label {
  position: absolute;
  max-width: 250px;
  min-height: 50px;
  color: #fff;
  background: rgba(1, 1, 1, 0.7);
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  border: rgba(1, 1, 1, 0.7) solid 2px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
  z-index: 90000;
  transition: 0.1s all;
}

.dpu-annotation-label:before {
  content: "";
  position: absolute;
  border-style: solid;
  border-width: 9px 13px 9px 0;
  border-color: transparent rgba(1, 1, 1, 0.7);
  display: block;
  width: 0;
  z-index: 0;
  margin-top: -12px;
  left: -15px;
  top: 20px;
}

.dpu-annotation-label.active {
  border: rgb(255 255 255 / 80%) solid 2px;
  box-shadow: 0px 4px 10px rgb(255 255 255 / 30%);
}

.dpu-annotation-label.active:before {
  border-color: transparent rgb(255 255 255 / 80%);
}

.dpu-annotation-label .dpu-annotation-content {
  white-space: nowrap;
  display: flex;
}
.dpu-annotation-content :nth-child(1) {
  width: 17px;
  font-weight: bold;
  padding: 0px 1px;
}
.dpu-content-x {
  background-color: red;
}

.dpu-content-y {
  background-color: blue;
}
.dpu-content-z {
  background-color: green;
}
